import React, { useEffect, useRef } from 'react';
import { View, ScrollView, RefreshControl } from 'react-native';
import { Appbar, Badge } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'react-native';
import { Colors } from '../../constants/Colors';
import createRecipeStyles from '@/styles/RecipeStyles';
import ShoppingCart from '../../assets/images/icons/shopping-cart.svg';
import Camera from '../../assets/images/icons/camera.svg';
import { getThemeColors } from '@/styles/Theme';
import { useGroceryList } from '@/contexts/GroceryListContext';

// Custom hooks
import { useRecipeManager } from '@/hooks/useRecipeManager';
import { useFavorites } from '@/hooks/useFavorites';
import { useRecipePreferences } from '@/hooks/useRecipePreferences';

// Components
import RecipeSourceTabs from '@/components/RecipeSourceTabs';
import MealTypeFilter from '@/components/MealTypeFilter';
import RecipeList from '@/components/RecipeList';

// Constants and types
import { Recipe, InstructionType } from '@/components/types';

export default function RecipeTab() {
  const colorScheme = useColorScheme() || 'light';
  const router = useRouter();
  const recipeStyles = createRecipeStyles(colorScheme as 'light' | 'dark');
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const { groceryItemCount } = useGroceryList();
  const scrollViewRef = useRef<ScrollView>(null);

  // Custom hooks for state management
  const recipeManager = useRecipeManager();
  const { favorites, toggleFavorite, loadingFavorites, generatingDetails } = useFavorites({
    onRecipeSaved: recipeManager.refreshSavedRecipes,
    onRecipeDetailsGenerated: recipeManager.updateRecipeDetails,
  });
  const recipePreferences = useRecipePreferences();

  // Instruction types for the expanded cards
  const instructionTypes: InstructionType[] = [
    InstructionType.HIGH_LEVEL,
    InstructionType.DETAILED,
    InstructionType.TEACH_MODE,
  ];

  // Function to scroll to top
  const scrollToTop = () => {
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
  };

  // Refresh saved recipes when favorites change and we're on the favorites tab
  useEffect(() => {
    if (recipeManager.selectedSource === 'Favorites' && !loadingFavorites) {
      recipeManager.refreshSavedRecipes();
    }
  }, [favorites, recipeManager.selectedSource, loadingFavorites]);

  return (
    <View style={recipeStyles.container}>
      {/* Header */}
      <Appbar.Header style={recipeStyles.header}>
        <Appbar.Action icon={() => <Camera fill={Colors[colorScheme].tint} />} onPress={() => router.push('/camera')} />

        {/* Recipe Source Tabs in Header - Centered */}
        <View style={{ flex: 1 }}>
          <RecipeSourceTabs
            selectedSource={recipeManager.selectedSource}
            onSourceChange={recipeManager.setSelectedSource}
            onTabPress={scrollToTop}
          />
        </View>

        <View>
          <Appbar.Action
            icon={() => <ShoppingCart fill={Colors[colorScheme].tint} />}
            onPress={() => router.push('/grocery-list')}
          />
          {groceryItemCount > 0 && (
            <Badge
              visible={true}
              size={20}
              style={{
                position: 'absolute',
                top: 5,
                right: 5,
                backgroundColor: colors.accent,
              }}
            >
              {groceryItemCount}
            </Badge>
          )}
        </View>
      </Appbar.Header>

      {/* Main Content */}
      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={recipeStyles.mainScrollContainer}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={recipeManager.onRefresh}
            colors={[colors.accent]}
            tintColor={colors.accent}
          />
        }
        onScroll={({ nativeEvent }) => {
          // Check if user has scrolled to the bottom
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const paddingToBottom = 20;
          if (
            layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom &&
            !recipeManager.loadingMore &&
            recipeManager.canLoadMore
          ) {
            console.log('>>>>>loading more');
            recipeManager.loadMoreRecipes();
          }
        }}
        scrollEventThrottle={400}
      >
        {/* Meal Type Filter Dropdown */}
        <MealTypeFilter
          selectedMealType={recipeManager.selectedMealType}
          onMealTypeChange={recipeManager.setSelectedMealType}
        />

        {/* Recipe List */}
        <RecipeList
          recipes={recipeManager.filteredRecipes}
          isLoading={recipeManager.isLoading}
          refreshing={recipeManager.refreshing}
          loadingMore={recipeManager.loadingMore}
          error={recipeManager.error}
          canLoadMore={recipeManager.canLoadMore}
          expandedRecipeIds={recipeManager.expandedRecipeIds}
          favorites={favorites}
          generatingDetails={generatingDetails}
          servings={recipePreferences.servings}
          instructionType={recipePreferences.instructionType}
          instructionTypes={instructionTypes}
          loadingRecipeDetails={recipeManager.loadingRecipeDetails}
          lastResponseId={recipeManager.lastResponseId}
          isPollingForFirstTimeRecipes={recipeManager.isPollingForFirstTimeRecipes}
          onToggleFavorite={(recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) =>
            toggleFavorite(recipeId, e, recipe, lastResponseId)
          }
          onToggleExpanded={recipeManager.toggleExpanded}
          onSelectInstructionType={recipePreferences.setInstructionType}
          onChangeServings={recipePreferences.setServings}
        />
      </ScrollView>
    </View>
  );
}
