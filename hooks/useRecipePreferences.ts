import { useState } from 'react';
import { InstructionType } from '@/components/types';

export interface UseRecipePreferencesReturn {
  servings: number;
  instructionType: InstructionType;
  setServings: (servings: number) => void;
  setInstructionType: (type: InstructionType) => void;
}

export const useRecipePreferences = (): UseRecipePreferencesReturn => {
  const [servings, setServings] = useState(1);
  const [instructionType, setInstructionType] = useState<InstructionType>(InstructionType.HIGH_LEVEL);

  return {
    servings,
    instructionType,
    setServings,
    setInstructionType,
  };
};
